import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Row,
  Col,
  Card,
  <PERSON><PERSON>,
  Badge,
  <PERSON>ert,
  Spinner,
  Table,
  Tabs,
  Tab,
  Form,
  InputGroup,
  Dropdown,
  ButtonGroup
} from 'react-bootstrap';
import { FaSearch, FaFilter, FaEye, FaEdit, FaCalendarAlt, FaDownload, FaSort, FaSortUp, FaSortDown } from 'react-icons/fa';
import { fetchOrders, fetchOrderStatuses } from '../../services/orderService';

// Helper function to map status codes to labels (same as in orderService)
const getStatusLabel = (status) => {
  const statusMap = {
    en_attente: 'En attente',
    confirmee: 'Confirmée',
    en_preparation: 'En préparation',
    expediee: 'Expédiée',
    livree: 'Livrée',
    annulee: 'Annulée',
    remboursee: 'Remboursée',
    retournee: 'Retournée'
  };

  return statusMap[status] || status || 'En attente';
};

const OrderList = () => {
  const navigate = useNavigate();

  // State
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [orderStatuses, setOrderStatuses] = useState([]);
  const [filterParams, setFilterParams] = useState({
    user_id: '',
    date_debut: '',
    date_fin: '',
    page: 1,
    per_page: 10
  });
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');

  // Load orders
  const loadOrders = async () => {
    setLoading(true);
    setError('');
    try {
      const data = await fetchOrders(filterParams);
      console.log('Orders loaded:', data); // Debug log
      setOrders(data);
    } catch (e) {
      setError(`Erreur lors du chargement des commandes: ${e.message}`);
    }
    setLoading(false);
  };

  // Load order statuses
  const loadOrderStatuses = async () => {
    try {
      const data = await fetchOrderStatuses();
      setOrderStatuses(data);
    } catch (e) {
      console.error('Erreur lors du chargement des statuts de commande:', e);
    }
  };

  // Initial data load
  useEffect(() => {
    console.log('Component mounted, loading data...');

    // Try to load from API first
    loadOrders();
    loadOrderStatuses();
  }, []);

  // Debug effect to log orders when they change
  useEffect(() => {
    console.log('Orders state updated:', orders);
    if (orders.length === 0) {
      console.log('No orders found, will try to use mock data');
    }
  }, [orders]);

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilterParams({
      ...filterParams,
      [name]: value
    });
  };

  // Apply filters
  const applyFilters = (e) => {
    e.preventDefault();
    loadOrders();
  };

  // Reset filters
  const resetFilters = () => {
    setFilterParams({
      user_id: '',
      date_debut: '',
      date_fin: '',
      page: 1,
      per_page: 10
    });
    setSearchTerm('');
    loadOrders();
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="text-muted" />;
    return sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />;
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  // Format price
  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  };

  // Get status badge variant
  const getStatusVariant = (status) => {
    if (!status) return 'secondary';

    // Direct mapping for common statuses
    const statusColorMap = {
      'En attente': 'warning',
      'En cours de traitement': 'info',
      Expédiée: 'primary',
      Livrée: 'success',
      Annulée: 'danger',
      Remboursée: 'secondary',
      // English equivalents
      pending: 'warning',
      processing: 'info',
      shipped: 'primary',
      delivered: 'success',
      cancelled: 'danger',
      refunded: 'secondary',
      completed: 'success'
    };

    // Check direct mapping first
    if (statusColorMap[status]) {
      return statusColorMap[status];
    }

    // Then check in orderStatuses
    const statusObj = orderStatuses.find((s) => s.name === status || s.name.toLowerCase() === status.toLowerCase());

    return statusObj?.color || 'secondary';
  };

  // Filter orders by status for tabs
  const getFilteredOrders = () => {
    if (!orders || orders.length === 0) {
      console.log('No orders to filter');
      return [];
    }

    console.log('Filtering orders by tab:', activeTab);

    if (activeTab === 'all') return orders;

    // Map tab keys to API status values
    const statusMap = {
      pending: 'en_attente',
      confirmed: 'confirmee',
      processing: 'en_preparation',
      shipped: 'expediee',
      delivered: 'livree',
      cancelled: 'annulee',
      refunded: 'remboursee',
      returned: 'retournee'
    };

    const targetStatusCode = statusMap[activeTab];
    const targetStatusLabel = getStatusLabel(targetStatusCode);

    const filtered = orders.filter((order) => {
      // Check if the order status matches
      return (
        // Check the converted status label
        order.status === targetStatusLabel ||
        // Check the original API status code
        (order._original && order._original.status === targetStatusCode) ||
        // Fallback checks
        order.status.toLowerCase().includes(activeTab.toLowerCase())
      );
    });

    console.log('Filtered orders:', filtered);
    return filtered;
  };

  // View order details
  const viewOrderDetails = (orderId) => {
    navigate(`/orders/${orderId}`);
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">Gestion des Commandes</h2>
          <p className="text-muted mb-0">Gérez et suivez toutes les commandes clients</p>
        </div>
        <div>
          <Button variant="primary" onClick={() => navigate('/orders/new')}>
            Nouvelle Commande
          </Button>
        </div>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}

      {/* Filters */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body>
          <Form onSubmit={applyFilters}>
            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Recherche</Form.Label>
                  <InputGroup>
                    <Form.Control
                      type="text"
                      placeholder="Rechercher..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button variant="outline-secondary">
                      <FaSearch />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Client ID</Form.Label>
                  <Form.Control
                    type="text"
                    name="user_id"
                    value={filterParams.user_id}
                    onChange={handleFilterChange}
                    placeholder="ID du client"
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de début</Form.Label>
                  <InputGroup>
                    <Form.Control type="date" name="date_debut" value={filterParams.date_debut} onChange={handleFilterChange} />
                    <Button variant="outline-secondary">
                      <FaCalendarAlt />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de fin</Form.Label>
                  <InputGroup>
                    <Form.Control type="date" name="date_fin" value={filterParams.date_fin} onChange={handleFilterChange} />
                    <Button variant="outline-secondary">
                      <FaCalendarAlt />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
            </Row>
            <div className="d-flex justify-content-end">
              <Button variant="outline-secondary" className="me-2" onClick={resetFilters}>
                Réinitialiser
              </Button>
              <Button variant="primary" type="submit">
                <FaFilter className="me-2" />
                Filtrer
              </Button>
            </div>
          </Form>
        </Card.Body>
      </Card>

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
            <Tab eventKey="all" title="Toutes les commandes" />
            <Tab eventKey="pending" title="En attente" />
            <Tab eventKey="confirmed" title="Confirmées" />
            <Tab eventKey="processing" title="En préparation" />
            <Tab eventKey="shipped" title="Expédiées" />
            <Tab eventKey="delivered" title="Livrées" />
            <Tab eventKey="cancelled" title="Annulées" />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Orders Table */}
      <Card className="shadow-sm border-0">
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3">Chargement des commandes...</p>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-5">
              <p className="mb-0">Aucune commande trouvée</p>
            </div>
          ) : (
            <Table responsive hover className="mb-0">
              <thead>
                <tr>
                  <th>N° Commande</th>
                  <th>Client</th>
                  <th>Date</th>
                  <th>Total</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {getFilteredOrders().map((order) => (
                  <tr key={order.id}>
                    <td>{order.order_number}</td>
                    <td>
                      <div>
                        <div className="fw-bold">{order.customer_name}</div>
                        <div className="small text-muted">{order.customer_email}</div>
                      </div>
                    </td>
                    <td>{new Date(order.created_at).toLocaleDateString()}</td>
                    <td>{order.total.toFixed(2)} €</td>
                    <td>
                      <Badge bg={getStatusVariant(order.status)}>{order.status}</Badge>
                    </td>
                    <td>
                      <ButtonGroup>
                        <Button variant="outline-primary" size="sm" onClick={() => viewOrderDetails(order.id)}>
                          <i className="fas fa-eye"></i>
                        </Button>
                        <Button variant="outline-primary" size="sm" onClick={() => navigate(`/orders/${order.id}/edit`)}>
                          <i className="fas fa-edit"></i>
                        </Button>
                        <Button variant="outline-danger" size="sm" onClick={() => navigate(`/orders/${order.id}/delete`)}>
                          <i className="fas fa-trash"></i>
                        </Button>
                      </ButtonGroup>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
        <Card.Footer className="bg-white">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              Affichage de {activeTab === 'all' ? orders.length : getFilteredOrders().length} commande(s)
              {orders.length > 0 && activeTab !== 'all' && <span className="text-muted ms-2">sur un total de {orders.length}</span>}
            </div>
            <div>{/* Pagination would go here */}</div>
          </div>
        </Card.Footer>
      </Card>
    </Container>
  );
};

export default OrderList;
