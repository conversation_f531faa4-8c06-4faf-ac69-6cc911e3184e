import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Row,
  Col,
  Card,
  <PERSON>ton,
  Badge,
  <PERSON><PERSON>,
  Spinner,
  Table,
  Tabs,
  Tab,
  Form,
  InputGroup,
  Dropdown,
  ButtonGroup,
  Modal
} from 'react-bootstrap';
import { FaSearch, FaFilter, FaEye, FaEdit, FaCalendarAlt, FaDownload, FaSort, FaSortUp, FaSortDown } from 'react-icons/fa';
import { fetchOrders, fetchOrderStatuses } from '../../services/orderService';

// Helper function to map status codes to labels (same as in orderService)
const getStatusLabel = (status) => {
  const statusMap = {
    en_attente: 'En attente',
    confirmee: 'Confirmée',
    en_preparation: 'En préparation',
    expediee: 'Expédiée',
    livree: 'Livrée',
    annulee: 'Annulée',
    remboursee: 'Remboursée',
    retournee: 'Retournée'
  };

  return statusMap[status] || status || 'En attente';
};

// Helper function to get status code from tab
const getStatusCodeFromTab = (tab) => {
  const tabToStatusMap = {
    pending: 'en_attente',
    confirmed: 'confirmee',
    processing: 'en_preparation',
    shipped: 'expediee',
    delivered: 'livree',
    cancelled: 'annulee',
    refunded: 'remboursee',
    returned: 'retournee'
  };

  return tabToStatusMap[tab];
};

const OrderList = () => {
  const navigate = useNavigate();

  // State
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [orderStatuses, setOrderStatuses] = useState([]);
  const [filterParams, setFilterParams] = useState({
    user_id: '',
    date_debut: '',
    date_fin: '',
    page: 1,
    per_page: 15
  });
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination state
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0,
    from: 0,
    to: 0
  });

  // Action states
  const [actionLoading, setActionLoading] = useState({});
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [newStatus, setNewStatus] = useState('');
  const [statusNotes, setStatusNotes] = useState('');

  // Load orders
  const loadOrders = async () => {
    setLoading(true);
    setError('');
    try {
      // Add sorting parameters
      const params = {
        ...filterParams,
        sort_by: sortField,
        sort_direction: sortDirection,
        status: activeTab !== 'all' ? getStatusCodeFromTab(activeTab) : undefined
      };

      const response = await fetchOrders(params);
      console.log('Orders loaded:', response); // Debug log

      // Handle paginated response
      if (response && typeof response === 'object' && response.data) {
        // Paginated response
        setOrders(response.data);
        setPagination({
          current_page: response.current_page || 1,
          last_page: response.last_page || 1,
          per_page: response.per_page || 15,
          total: response.total || 0,
          from: response.from || 0,
          to: response.to || 0
        });
      } else if (Array.isArray(response)) {
        // Direct array response
        setOrders(response);
        setPagination({
          current_page: 1,
          last_page: 1,
          per_page: response.length,
          total: response.length,
          from: 1,
          to: response.length
        });
      } else {
        setOrders([]);
      }
    } catch (e) {
      setError(`Erreur lors du chargement des commandes: ${e.message}`);
      setOrders([]);
    }
    setLoading(false);
  };

  // Load order statuses
  const loadOrderStatuses = async () => {
    try {
      const data = await fetchOrderStatuses();
      setOrderStatuses(data);
    } catch (e) {
      console.error('Erreur lors du chargement des statuts de commande:', e);
    }
  };

  // Initial data load
  useEffect(() => {
    console.log('Component mounted, loading data...');
    loadOrderStatuses();
  }, []);

  // Reload orders when filter parameters change
  useEffect(() => {
    loadOrders();
  }, [filterParams, sortField, sortDirection, activeTab]);

  // Debug effect to log orders when they change
  useEffect(() => {
    console.log('Orders state updated:', orders);
    if (orders.length === 0) {
      console.log('No orders found, will try to use mock data');
    }
  }, [orders]);

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilterParams({
      ...filterParams,
      [name]: value
    });
  };

  // Apply filters
  const applyFilters = (e) => {
    e.preventDefault();
    loadOrders();
  };

  // Reset filters
  const resetFilters = () => {
    setFilterParams({
      user_id: '',
      date_debut: '',
      date_fin: '',
      page: 1,
      per_page: 10
    });
    setSearchTerm('');
    loadOrders();
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="text-muted" />;
    return sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />;
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  // Format price
  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  };

  // Get status badge variant
  const getStatusVariant = (status) => {
    if (!status) return 'secondary';

    // Direct mapping for common statuses
    const statusColorMap = {
      'En attente': 'warning',
      'En cours de traitement': 'info',
      Expédiée: 'primary',
      Livrée: 'success',
      Annulée: 'danger',
      Remboursée: 'secondary',
      // English equivalents
      pending: 'warning',
      processing: 'info',
      shipped: 'primary',
      delivered: 'success',
      cancelled: 'danger',
      refunded: 'secondary',
      completed: 'success'
    };

    // Check direct mapping first
    if (statusColorMap[status]) {
      return statusColorMap[status];
    }

    // Then check in orderStatuses
    const statusObj = orderStatuses.find((s) => s.name === status || s.name.toLowerCase() === status.toLowerCase());

    return statusObj?.color || 'secondary';
  };

  // Filter orders by status for tabs
  const getFilteredOrders = () => {
    if (!orders || orders.length === 0) {
      console.log('No orders to filter');
      return [];
    }

    console.log('Filtering orders by tab:', activeTab);

    if (activeTab === 'all') return orders;

    // Map tab keys to API status values
    const statusMap = {
      pending: 'en_attente',
      confirmed: 'confirmee',
      processing: 'en_preparation',
      shipped: 'expediee',
      delivered: 'livree',
      cancelled: 'annulee',
      refunded: 'remboursee',
      returned: 'retournee'
    };

    const targetStatusCode = statusMap[activeTab];
    const targetStatusLabel = getStatusLabel(targetStatusCode);

    const filtered = orders.filter((order) => {
      // Check if the order status matches
      return (
        // Check the converted status label
        order.status === targetStatusLabel ||
        // Check the original API status code
        (order._original && order._original.status === targetStatusCode) ||
        // Fallback checks
        order.status.toLowerCase().includes(activeTab.toLowerCase())
      );
    });

    console.log('Filtered orders:', filtered);
    return filtered;
  };

  // Pagination handlers
  const handlePageChange = (page) => {
    setFilterParams((prev) => ({ ...prev, page }));
  };

  const handlePerPageChange = (perPage) => {
    setFilterParams((prev) => ({ ...prev, per_page: perPage, page: 1 }));
  };

  // Action handlers
  const handleStatusChange = (order) => {
    setSelectedOrder(order);
    setNewStatus(order._original?.status || 'en_attente');
    setStatusNotes('');
    setShowStatusModal(true);
  };

  const handleUpdateStatus = async () => {
    if (!selectedOrder || !newStatus) return;

    setActionLoading((prev) => ({ ...prev, [`status_${selectedOrder.id}`]: true }));
    try {
      const { updateOrderStatus } = await import('../../services/orderService');
      await updateOrderStatus(selectedOrder.id, newStatus, statusNotes);
      setShowStatusModal(false);
      loadOrders(); // Reload orders
    } catch (error) {
      setError(`Erreur lors de la mise à jour du statut: ${error.message}`);
    }
    setActionLoading((prev) => ({ ...prev, [`status_${selectedOrder.id}`]: false }));
  };

  const handleCancelOrder = async (order) => {
    if (!window.confirm('Êtes-vous sûr de vouloir annuler cette commande ?')) return;

    setActionLoading((prev) => ({ ...prev, [`cancel_${order.id}`]: true }));
    try {
      const { cancelOrder } = await import('../../services/orderService');
      await cancelOrder(order.id, "Annulée par l'administrateur");
      loadOrders(); // Reload orders
    } catch (error) {
      setError(`Erreur lors de l'annulation: ${error.message}`);
    }
    setActionLoading((prev) => ({ ...prev, [`cancel_${order.id}`]: false }));
  };

  const handleDeleteOrder = async (order) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette commande ? Cette action est irréversible.')) return;

    setActionLoading((prev) => ({ ...prev, [`delete_${order.id}`]: true }));
    try {
      const { deleteOrder } = await import('../../services/orderService');
      await deleteOrder(order.id);
      loadOrders(); // Reload orders
    } catch (error) {
      setError(`Erreur lors de la suppression: ${error.message}`);
    }
    setActionLoading((prev) => ({ ...prev, [`delete_${order.id}`]: false }));
  };

  // View order details
  const viewOrderDetails = (orderId) => {
    navigate(`/orders/${orderId}`);
  };

  // Export orders
  const handleExportOrders = () => {
    // TODO: Implement export functionality
    alert("Fonctionnalité d'export en cours de développement");
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">Gestion des Commandes</h2>
          <p className="text-muted mb-0">Gérez et suivez toutes les commandes clients</p>
        </div>
        <div>
          <Button variant="outline-secondary" className="me-2" onClick={handleExportOrders}>
            <FaDownload className="me-2" />
            Exporter
          </Button>
          <Button variant="primary" onClick={() => navigate('/orders/new')}>
            Nouvelle Commande
          </Button>
        </div>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}

      {/* Filters */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body>
          <Form onSubmit={applyFilters}>
            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Recherche</Form.Label>
                  <InputGroup>
                    <Form.Control
                      type="text"
                      placeholder="Rechercher..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button variant="outline-secondary">
                      <FaSearch />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Client ID</Form.Label>
                  <Form.Control
                    type="text"
                    name="user_id"
                    value={filterParams.user_id}
                    onChange={handleFilterChange}
                    placeholder="ID du client"
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de début</Form.Label>
                  <InputGroup>
                    <Form.Control type="date" name="date_debut" value={filterParams.date_debut} onChange={handleFilterChange} />
                    <Button variant="outline-secondary">
                      <FaCalendarAlt />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de fin</Form.Label>
                  <InputGroup>
                    <Form.Control type="date" name="date_fin" value={filterParams.date_fin} onChange={handleFilterChange} />
                    <Button variant="outline-secondary">
                      <FaCalendarAlt />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
            </Row>
            <div className="d-flex justify-content-end">
              <Button variant="outline-secondary" className="me-2" onClick={resetFilters}>
                Réinitialiser
              </Button>
              <Button variant="primary" type="submit">
                <FaFilter className="me-2" />
                Filtrer
              </Button>
            </div>
          </Form>
        </Card.Body>
      </Card>

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
            <Tab eventKey="all" title="Toutes les commandes" />
            <Tab eventKey="pending" title="En attente" />
            <Tab eventKey="confirmed" title="Confirmées" />
            <Tab eventKey="processing" title="En préparation" />
            <Tab eventKey="shipped" title="Expédiées" />
            <Tab eventKey="delivered" title="Livrées" />
            <Tab eventKey="cancelled" title="Annulées" />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Orders Table */}
      <Card className="shadow-sm border-0">
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3">Chargement des commandes...</p>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-5">
              <p className="mb-0">Aucune commande trouvée</p>
            </div>
          ) : (
            <Table responsive hover className="mb-0">
              <thead>
                <tr>
                  <th>N° Commande</th>
                  <th>Client</th>
                  <th>Date</th>
                  <th>Total</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {getFilteredOrders().map((order) => (
                  <tr key={order.id}>
                    <td>{order.order_number}</td>
                    <td>
                      <div>
                        <div className="fw-bold">{order.customer_name}</div>
                        <div className="small text-muted">{order.customer_email}</div>
                      </div>
                    </td>
                    <td>{new Date(order.created_at).toLocaleDateString()}</td>
                    <td>{order.total.toFixed(2)} €</td>
                    <td>
                      <Badge bg={getStatusVariant(order.status)}>{order.status}</Badge>
                    </td>
                    <td>
                      <Dropdown as={ButtonGroup}>
                        <Button variant="outline-primary" size="sm" onClick={() => viewOrderDetails(order.id)} title="Voir les détails">
                          <FaEye />
                        </Button>

                        <Dropdown.Toggle split variant="outline-primary" size="sm" id={`dropdown-split-${order.id}`} />

                        <Dropdown.Menu>
                          <Dropdown.Item onClick={() => handleStatusChange(order)}>
                            <FaEdit className="me-2" />
                            Changer le statut
                          </Dropdown.Item>

                          <Dropdown.Item onClick={() => navigate(`/orders/${order.id}/edit`)}>
                            <FaEdit className="me-2" />
                            Modifier la commande
                          </Dropdown.Item>

                          <Dropdown.Divider />

                          {order._original?.status !== 'annulee' && (
                            <Dropdown.Item
                              onClick={() => handleCancelOrder(order)}
                              disabled={actionLoading[`cancel_${order.id}`]}
                              className="text-warning"
                            >
                              {actionLoading[`cancel_${order.id}`] ? (
                                <Spinner animation="border" size="sm" className="me-2" />
                              ) : (
                                <i className="fas fa-ban me-2"></i>
                              )}
                              Annuler la commande
                            </Dropdown.Item>
                          )}

                          <Dropdown.Item
                            onClick={() => handleDeleteOrder(order)}
                            disabled={actionLoading[`delete_${order.id}`]}
                            className="text-danger"
                          >
                            {actionLoading[`delete_${order.id}`] ? (
                              <Spinner animation="border" size="sm" className="me-2" />
                            ) : (
                              <i className="fas fa-trash me-2"></i>
                            )}
                            Supprimer
                          </Dropdown.Item>
                        </Dropdown.Menu>
                      </Dropdown>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
        <Card.Footer className="bg-white">
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              <span className="me-3">
                Affichage de {pagination.from} à {pagination.to} sur {pagination.total} commande(s)
              </span>
              <Form.Select
                size="sm"
                style={{ width: 'auto' }}
                value={pagination.per_page}
                onChange={(e) => handlePerPageChange(parseInt(e.target.value))}
              >
                <option value={10}>10 par page</option>
                <option value={15}>15 par page</option>
                <option value={25}>25 par page</option>
                <option value={50}>50 par page</option>
                <option value={100}>100 par page</option>
              </Form.Select>
            </div>

            {pagination.last_page > 1 && (
              <nav>
                <ul className="pagination pagination-sm mb-0">
                  <li className={`page-item ${pagination.current_page === 1 ? 'disabled' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(1)} disabled={pagination.current_page === 1}>
                      Premier
                    </button>
                  </li>
                  <li className={`page-item ${pagination.current_page === 1 ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => handlePageChange(pagination.current_page - 1)}
                      disabled={pagination.current_page === 1}
                    >
                      Précédent
                    </button>
                  </li>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, pagination.last_page) }, (_, i) => {
                    let pageNum;
                    if (pagination.last_page <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.current_page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.current_page >= pagination.last_page - 2) {
                      pageNum = pagination.last_page - 4 + i;
                    } else {
                      pageNum = pagination.current_page - 2 + i;
                    }

                    return (
                      <li key={pageNum} className={`page-item ${pagination.current_page === pageNum ? 'active' : ''}`}>
                        <button className="page-link" onClick={() => handlePageChange(pageNum)}>
                          {pageNum}
                        </button>
                      </li>
                    );
                  })}

                  <li className={`page-item ${pagination.current_page === pagination.last_page ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => handlePageChange(pagination.current_page + 1)}
                      disabled={pagination.current_page === pagination.last_page}
                    >
                      Suivant
                    </button>
                  </li>
                  <li className={`page-item ${pagination.current_page === pagination.last_page ? 'disabled' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => handlePageChange(pagination.last_page)}
                      disabled={pagination.current_page === pagination.last_page}
                    >
                      Dernier
                    </button>
                  </li>
                </ul>
              </nav>
            )}
          </div>
        </Card.Footer>
      </Card>

      {/* Status Change Modal */}
      <Modal show={showStatusModal} onHide={() => setShowStatusModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Changer le statut de la commande</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedOrder && (
            <div>
              <p>
                <strong>Commande:</strong> {selectedOrder.order_number}
              </p>
              <p>
                <strong>Client:</strong> {selectedOrder.customer_name}
              </p>
              <p>
                <strong>Statut actuel:</strong> <Badge bg={getStatusVariant(selectedOrder.status)}>{selectedOrder.status}</Badge>
              </p>

              <Form.Group className="mb-3">
                <Form.Label>Nouveau statut</Form.Label>
                <Form.Select value={newStatus} onChange={(e) => setNewStatus(e.target.value)}>
                  {orderStatuses.map((status) => (
                    <option key={status.id} value={status.value}>
                      {status.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Notes (optionnel)</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={statusNotes}
                  onChange={(e) => setStatusNotes(e.target.value)}
                  placeholder="Ajoutez des notes sur ce changement de statut..."
                />
              </Form.Group>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowStatusModal(false)}>
            Annuler
          </Button>
          <Button variant="primary" onClick={handleUpdateStatus} disabled={actionLoading[`status_${selectedOrder?.id}`] || !newStatus}>
            {actionLoading[`status_${selectedOrder?.id}`] ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Mise à jour...
              </>
            ) : (
              'Mettre à jour'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default OrderList;
