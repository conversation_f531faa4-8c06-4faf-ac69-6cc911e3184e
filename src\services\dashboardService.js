const API_URL = import.meta.env.VITE_REACT_APP_API_URL || import.meta.env.REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Dashboard metrics service
export async function fetchDashboardMetrics() {
  try {
    console.log('🔄 Fetching dashboard data from API:', API_URL);

    // Fetch all required data in parallel
    const [productsRes, ordersRes, clientsRes] = await Promise.all([
      fetch(`${API_URL}/produits`).catch(err => {
        console.error('❌ Products fetch failed:', err);
        return { ok: false, status: 'network_error' };
      }),
      fetch(`${API_URL}/commandes`).catch(err => {
        console.error('❌ Orders fetch failed:', err);
        return { ok: false, status: 'network_error' };
      }),
      fetch(`${API_URL}/clients`).catch(err => {
        console.error('❌ Clients fetch failed:', err);
        return { ok: false, status: 'network_error' };
      })
    ]);

    console.log('📊 API responses:', {
      products: { ok: productsRes.ok, status: productsRes.status },
      orders: { ok: ordersRes.ok, status: ordersRes.status },
      clients: { ok: clientsRes.ok, status: clientsRes.status }
    });

    // Handle individual failures gracefully
    let productsData = { data: [] };
    let ordersData = { data: [] };
    let clientsData = { data: [] };

    if (productsRes.ok) {
      try {
        productsData = await productsRes.json();
      } catch (err) {
        console.error('❌ Failed to parse products JSON:', err);
      }
    }

    if (ordersRes.ok) {
      try {
        ordersData = await ordersRes.json();
      } catch (err) {
        console.error('❌ Failed to parse orders JSON:', err);
      }
    }

    if (clientsRes.ok) {
      try {
        clientsData = await clientsRes.json();
      } catch (err) {
        console.error('❌ Failed to parse clients JSON:', err);
      }
    }

    const [productsData, ordersData, clientsData] = await Promise.all([
      productsRes.json(),
      ordersRes.json(),
      clientsRes.json()
    ]);

    // Extract totals
    const totalProducts = Array.isArray(productsData.data) ? productsData.data.length :
                         Array.isArray(productsData) ? productsData.length : 0;

    const totalOrders = Array.isArray(ordersData.data) ? ordersData.data.length :
                       Array.isArray(ordersData) ? ordersData.length : 0;

    const totalClients = Array.isArray(clientsData.data) ? clientsData.data.length :
                        Array.isArray(clientsData) ? clientsData.length : 0;

    // Get recent orders (last 5)
    const ordersArray = ordersData.data || ordersData || [];
    const recentOrders = Array.isArray(ordersArray) ?
      ordersArray
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5)
        .map(order => ({
          id: order.id,
          order_number: order.numero_commande || `CMD-${order.id}`,
          customer_name: order.nom_client || order.user?.name || 'Client inconnu',
          total: parseFloat(order.total_commande || order.total || 0),
          status: order.statut_commande || order.status || 'En attente',
          created_at: order.created_at
        })) : [];

    // Calculate sales data for the chart (last 6 months)
    const salesData = calculateSalesData(ordersArray);

    return {
      totalProducts,
      totalOrders,
      totalClients,
      recentOrders,
      salesData
    };
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    throw error;
  }
}

// Helper function to calculate sales data for charts
function calculateSalesData(orders) {
  if (!Array.isArray(orders)) return [];

  const now = new Date();
  const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 5, 1);

  // Initialize months array
  const months = [];
  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    months.push({
      month: date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' }),
      sales: 0,
      orderCount: 0
    });
  }

  // Aggregate sales by month
  orders.forEach(order => {
    const orderDate = new Date(order.created_at);
    if (orderDate >= sixMonthsAgo) {
      const monthIndex = months.findIndex(m => {
        const monthDate = new Date(orderDate.getFullYear(), orderDate.getMonth(), 1);
        const targetDate = new Date(now.getFullYear(), now.getMonth() - (5 - months.indexOf(m)), 1);
        return monthDate.getTime() === targetDate.getTime();
      });

      if (monthIndex !== -1) {
        months[monthIndex].sales += parseFloat(order.total_commande || order.total || 0);
        months[monthIndex].orderCount += 1;
      }
    }
  });

  return months;
}

// Fetch specific metrics
export async function fetchProductsCount() {
  try {
    const res = await fetch(`${API_URL}/produits`);
    if (!res.ok) throw new Error('Failed to fetch products count');

    const data = await res.json();
    return Array.isArray(data.data) ? data.data.length : Array.isArray(data) ? data.length : 0;
  } catch (error) {
    console.error('Error fetching products count:', error);
    return 0;
  }
}

export async function fetchOrdersCount() {
  try {
    const res = await fetch(`${API_URL}/commandes`);
    if (!res.ok) throw new Error('Failed to fetch orders count');

    const data = await res.json();
    return Array.isArray(data.data) ? data.data.length : Array.isArray(data) ? data.length : 0;
  } catch (error) {
    console.error('Error fetching orders count:', error);
    return 0;
  }
}

export async function fetchClientsCount() {
  try {
    const res = await fetch(`${API_URL}/clients`);
    if (!res.ok) throw new Error('Failed to fetch clients count');

    const data = await res.json();
    return Array.isArray(data.data) ? data.data.length : Array.isArray(data) ? data.length : 0;
  } catch (error) {
    console.error('Error fetching clients count:', error);
    return 0;
  }
}

export async function fetchRecentOrders(limit = 5) {
  try {
    const res = await fetch(`${API_URL}/commandes`);
    if (!res.ok) throw new Error('Failed to fetch recent orders');

    const data = await res.json();
    const ordersArray = data.data || data || [];

    return Array.isArray(ordersArray) ?
      ordersArray
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, limit)
        .map(order => ({
          id: order.id,
          order_number: order.numero_commande || `CMD-${order.id}`,
          customer_name: order.nom_client || order.user?.name || 'Client inconnu',
          total: parseFloat(order.total_commande || order.total || 0),
          status: order.statut_commande || order.status || 'En attente',
          created_at: order.created_at
        })) : [];
  } catch (error) {
    console.error('Error fetching recent orders:', error);
    return [];
  }
}
